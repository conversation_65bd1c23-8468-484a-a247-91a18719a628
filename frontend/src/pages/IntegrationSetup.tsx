// Explicitly import React to ensure it's available
import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

import { supabase } from '../services/supabaseClient';
import { useRolewiseStore } from '../stores/rolewiseStore';
import { useAuthStore } from '../stores/authStore';
import { EntraIdIntegrationProvider, useEntraIdIntegration } from '../contexts/EntraIdIntegrationContext';
import * as styles from './integrationSetupStyles.css.ts';

// Main component content that uses the integration context
const IntegrationSetupContent: React.FC = () => {
  // Add error handling for style imports
  if (!styles || !styles.container) {
    console.error('Styles not loaded properly:', styles);
  }

  const location = useLocation();
  const { state: integrationState, actions: integrationActions } = useEntraIdIntegration();
  const { loading: contextLoading, error: contextError } = useRolewiseStore();
  const { user } = useAuthStore();

  const [tenantId, setTenantId] = useState(''); // Entra ID tenant GUID
  const [clientId, setClientId] = useState('');
  const [clientSecret, setClientSecret] = useState('');
  const [appTenantId, setAppTenantId] = useState<string | null>(null); // App tenant ID
  const [testResult, setTestResult] = useState<string | null>(null);
  const [localLoading, setLocalLoading] = useState(false);
  const [existingIntegration, setExistingIntegration] = useState<boolean>(false);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [authenticationMode, setAuthenticationMode] = useState<'user' | 'app'>('app');

  useEffect(() => {
    const fetchAppTenantId = async () => {
      try {
        if (!user) {
          return;
        }

        const { data, error } = await supabase
          .from('profiles')
          .select('tenant_id')
          .eq('email', user.email)
          .single();

        if (error) {
          console.error('Failed to fetch app tenant ID:', error);
          setTestResult('Error: Could not determine your tenant ID.');
          return;
        }

        if (!data || !data.tenant_id) {
          setTestResult('Error: No tenant ID associated with your account.');
          return;
        }

        setAppTenantId(data.tenant_id);

        // Now check if there's an existing integration for this tenant
        fetchExistingIntegration(data.tenant_id);
      } catch (err) {
        console.error('Unexpected error in fetchAppTenantId:', err);
        setTestResult('Error: An unexpected error occurred while fetching your tenant ID.');
      }
    };

    const fetchExistingIntegration = async (rolewise_tenant_id: string) => {
      try {
        const { data, error } = await supabase
          .from('integrations')
          .select('app_tenant_id, client_id')
          .eq('tenant_id', rolewise_tenant_id)
          .maybeSingle();

        if (error) {
          if (error.code === 'PGRST116') {
            // No integration found - this is expected for new setups
            setExistingIntegration(false);
          } else {
            console.error('Error checking for existing integration:', error);
          }
          return;
        }

        if (data) {
          setExistingIntegration(true);

          // Populate the form with existing values - app_tenant_id is the Entra ID tenant ID
          setTenantId(data.app_tenant_id || '');
          setClientId(data.client_id || '');
          // We don't populate the client secret for security reasons
        } else {
          setExistingIntegration(false);
        }
      } catch (err) {
        console.error('Unexpected error checking for existing integration:', err);
      }
    };

    fetchAppTenantId();
  }, [user]);

  // Handle callback messages from integration authentication
  useEffect(() => {
    if (location.state) {
      const { authSuccess, message, error } = location.state as any;

      if (authSuccess && message) {
        setTestResult(message);
        integrationActions.setError(null);
      } else if (error) {
        setTestResult(`Error: ${error}`);
        integrationActions.setError(error);
      }

      // Clear the location state to prevent re-triggering
      window.history.replaceState({}, document.title);
    }
  }, [location.state, integrationActions]);

  const saveIntegration = async () => {
    if (!appTenantId) {
      setTestResult('Error: Could not determine your Rolewise tenant ID.');
      return;
    }

    setLocalLoading(true);
    try {
      // Prepare the data object - tenant_id is the Rolewise tenant ID, app_tenant_id is the Entra ID tenant ID
      const integrationData: any = {
        tenant_id: appTenantId, // Rolewise tenant ID
        app_tenant_id: tenantId, // Entra ID tenant ID
        client_id: clientId,
      };

      // Only include client_secret if it's provided (for updates, it might be empty)
      if (clientSecret) {
        integrationData.client_secret = clientSecret;
      }

      const { error } = await supabase
        .from('integrations')
        .upsert(integrationData);

      if (error) throw error;

      // Update UI state
      setExistingIntegration(true);
      setIsEditMode(false);
      setTestResult(`Integration ${existingIntegration ? 'updated' : 'created'} successfully!`);
    } catch (error) {
      console.error('Error saving integration:', error);
      setTestResult('Error: Failed to save integration.');
    } finally {
      setLocalLoading(false);
    }
  };

  const testConnection = async () => {
    if (!tenantId || !clientId || !appTenantId) {
      setTestResult('Error: Tenant ID and Client ID are required.');
      return;
    }

    if (!clientSecret && !existingIntegration && authenticationMode === 'app') {
      setTestResult('Error: Client Secret is required for app authentication.');
      return;
    }

    setLocalLoading(true);
    integrationActions.setError(null);

    try {
      const config = {
        tenantId,
        clientId,
        clientSecret: authenticationMode === 'app' ? clientSecret : undefined
      };

      console.log(`[Integration Setup] Testing connection with ${authenticationMode} authentication`);

      if (authenticationMode === 'user') {
        // Use user authentication flow (MSAL popup)
        const success = await integrationActions.authenticateIntegration(config);
        if (success) {
          setTestResult('User authentication successful! You can now save this integration.');
        } else {
          setTestResult('User authentication failed. Please try again.');
        }
      } else {
        // Use app authentication flow (client credentials)
        const success = await integrationActions.testConnection(config);
        if (success) {
          setTestResult('App authentication successful! Connection verified.');
        } else {
          setTestResult('App authentication failed. Please check your credentials.');
        }
      }
    } catch (error: any) {
      console.error('Error testing connection:', error);
      setTestResult(`Error: ${error.message || 'Failed to test connection'}`);
      integrationActions.setError(error.message || 'Connection test failed');
    } finally {
      setLocalLoading(false);
    }
  };

  // Check if any required dependencies are missing
  if (!supabase) {
    console.error('Supabase client is not initialized');
    return (
      <div style={{ padding: '20px', margin: '20px', backgroundColor: '#ffebee', border: '1px solid #ffcdd2', borderRadius: '5px' }}>
        <h2>Error: Supabase Client Not Initialized</h2>
        <p>The Supabase client is not properly initialized. Please check your configuration.</p>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <h1 className={styles.title}>Integration Setup</h1>

      {contextError && !existingIntegration && (
        <div className={styles.error}>
          <strong>Error:</strong> {contextError}
        </div>
      )}

      <div className={styles.content}>
        {contextLoading && <p className={styles.loading}>Loading from context...</p>}
        {localLoading && <p className={styles.loading}>Loading locally...</p>}

        <div className={styles.formGroup}>
          <p>
            {existingIntegration
              ? 'Your Microsoft Entra ID integration is configured. You can update it below.'
              : 'Configure your Microsoft Entra ID integration below to connect your tenant with Rolewise.ai.'}
          </p>

          {existingIntegration && !isEditMode && (
            <button
              onClick={() => setIsEditMode(true)}
              className={styles.button}
            >
              Edit Configuration
            </button>
          )}
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>
            Entra ID Tenant ID:
            <input
              type="text"
              value={tenantId}
              onChange={(e) => setTenantId(e.target.value)}
              disabled={localLoading || contextLoading || (existingIntegration && !isEditMode)}
              className={styles.input}
              placeholder="Enter your Entra ID Tenant ID"
            />
          </label>
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>
            Client ID:
            <input
              type="text"
              value={clientId}
              onChange={(e) => setClientId(e.target.value)}
              disabled={localLoading || contextLoading || (existingIntegration && !isEditMode)}
              className={styles.input}
              placeholder="Enter your Client ID"
            />
          </label>
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>
            Authentication Mode:
            <select
              value={authenticationMode}
              onChange={(e) => setAuthenticationMode(e.target.value as 'user' | 'app')}
              disabled={localLoading || contextLoading || (existingIntegration && !isEditMode)}
              className={styles.input}
            >
              <option value="app">App Authentication (Client Credentials)</option>
              <option value="user">User Authentication (Interactive Login)</option>
            </select>
          </label>
          <small style={{ color: '#666', fontSize: '12px', marginTop: '4px', display: 'block' }}>
            {authenticationMode === 'app'
              ? 'Uses client secret for server-to-server authentication'
              : 'Requires interactive login to your Entra ID tenant'
            }
          </small>
        </div>

        {authenticationMode === 'app' && (
          <div className={styles.formGroup}>
            <label className={styles.label}>
              Client Secret:
              <input
                type="password"
                value={clientSecret}
                onChange={(e) => setClientSecret(e.target.value)}
                disabled={localLoading || contextLoading || (existingIntegration && !isEditMode)}
                className={styles.input}
                placeholder={existingIntegration ? "Enter new Client Secret (leave blank to keep current)" : "Enter your Client Secret"}
              />
            </label>
          </div>
        )}

        <div className={styles.buttonGroup}>
          {(!existingIntegration || isEditMode) && (
            <button
              onClick={saveIntegration}
              disabled={localLoading || contextLoading || !appTenantId}
              className={styles.button}
            >
              {localLoading ? 'Saving...' : existingIntegration ? 'Update Integration' : 'Save Integration'}
            </button>
          )}

          {isEditMode && (
            <button
              onClick={() => setIsEditMode(false)}
              disabled={localLoading || contextLoading}
              className={styles.button}
            >
              Cancel
            </button>
          )}

          <button
            onClick={testConnection}
            disabled={
              localLoading ||
              contextLoading ||
              !tenantId ||
              !clientId ||
              (authenticationMode === 'app' && !clientSecret && !existingIntegration) ||
              !appTenantId
            }
            className={styles.button}
          >
            {localLoading ? 'Testing...' : `Test ${authenticationMode === 'user' ? 'User Authentication' : 'Connection'}`}
          </button>
        </div>

        {/* Integration authentication status */}
        {integrationState.isAuthenticated && (
          <div className={styles.success}>
            ✅ Authenticated to {integrationState.accountInfo?.username || 'Entra ID tenant'}
            <button
              onClick={integrationActions.clearAuthentication}
              style={{
                marginLeft: '10px',
                padding: '4px 8px',
                fontSize: '12px',
                backgroundColor: '#f44336',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Clear Auth
            </button>
          </div>
        )}

        {integrationState.error && (
          <div className={styles.error}>
            Integration Error: {integrationState.error}
          </div>
        )}

        {testResult && (
          <div className={`${styles.result} ${testResult.includes('Error') ? styles.error : styles.success}`}>
            {testResult}
          </div>
        )}
      </div>
    </div>
  );
};

// Wrapper component that provides the Entra ID integration context
const IntegrationSetup: React.FC = () => {
  return (
    <EntraIdIntegrationProvider>
      <IntegrationSetupContent />
    </EntraIdIntegrationProvider>
  );
};

export default IntegrationSetup;