// Explicitly import React to ensure it's available
import React, { useState, useEffect } from 'react';

import { supabase } from '../services/supabaseClient';
import { useRolewiseStore } from '../stores/rolewiseStore';
import { useAuthStore } from '../stores/authStore';
import { testEntraIdConnection } from '../services/api/entra-id-auth';
import * as styles from './integrationSetupStyles.css.ts';

// Use React.FC type to ensure proper typing
const IntegrationSetup: React.FC = () => {
  // Add error handling for style imports
  if (!styles || !styles.container) {
    console.error('Styles not loaded properly:', styles);
  }
  const [tenantId, setTenantId] = useState(''); // Entra ID tenant GUID
  const [clientId, setClientId] = useState('');
  const [clientSecret, setClientSecret] = useState('');
  const [appTenantId, setAppTenantId] = useState<string | null>(null); // App tenant ID
  const [testResult, setTestResult] = useState<string | null>(null);
  const [localLoading, setLocalLoading] = useState(false);
  const [existingIntegration, setExistingIntegration] = useState<boolean>(false);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const { loading: contextLoading, error: contextError } = useRolewiseStore();
  const { user } = useAuthStore();

  useEffect(() => {
    const fetchAppTenantId = async () => {
      try {
        if (!user) {
          return;
        }

        const { data, error } = await supabase
          .from('profiles')
          .select('tenant_id')
          .eq('email', user.email)
          .single();

        if (error) {
          console.error('Failed to fetch app tenant ID:', error);
          setTestResult('Error: Could not determine your tenant ID.');
          return;
        }

        if (!data || !data.tenant_id) {
          setTestResult('Error: No tenant ID associated with your account.');
          return;
        }

        setAppTenantId(data.tenant_id);

        // Now check if there's an existing integration for this tenant
        fetchExistingIntegration(data.tenant_id);
      } catch (err) {
        console.error('Unexpected error in fetchAppTenantId:', err);
        setTestResult('Error: An unexpected error occurred while fetching your tenant ID.');
      }
    };

    const fetchExistingIntegration = async (rolewise_tenant_id: string) => {
      try {
        const { data, error } = await supabase
          .from('integrations')
          .select('app_tenant_id, client_id')
          .eq('tenant_id', rolewise_tenant_id)
          .maybeSingle();

        if (error) {
          if (error.code === 'PGRST116') {
            // No integration found - this is expected for new setups
            setExistingIntegration(false);
          } else {
            console.error('Error checking for existing integration:', error);
          }
          return;
        }

        if (data) {
          setExistingIntegration(true);

          // Populate the form with existing values - app_tenant_id is the Entra ID tenant ID
          setTenantId(data.app_tenant_id || '');
          setClientId(data.client_id || '');
          // We don't populate the client secret for security reasons
        } else {
          setExistingIntegration(false);
        }
      } catch (err) {
        console.error('Unexpected error checking for existing integration:', err);
      }
    };

    fetchAppTenantId();
  }, [user]);

  const saveIntegration = async () => {
    if (!appTenantId) {
      setTestResult('Error: Could not determine your Rolewise tenant ID.');
      return;
    }

    setLocalLoading(true);
    try {
      // Prepare the data object - tenant_id is the Rolewise tenant ID, app_tenant_id is the Entra ID tenant ID
      const integrationData: any = {
        tenant_id: appTenantId, // Rolewise tenant ID
        app_tenant_id: tenantId, // Entra ID tenant ID
        client_id: clientId,
      };

      // Only include client_secret if it's provided (for updates, it might be empty)
      if (clientSecret) {
        integrationData.client_secret = clientSecret;
      }

      const { error } = await supabase
        .from('integrations')
        .upsert(integrationData);

      if (error) throw error;

      // Update UI state
      setExistingIntegration(true);
      setIsEditMode(false);
      setTestResult(`Integration ${existingIntegration ? 'updated' : 'created'} successfully!`);
    } catch (error) {
      console.error('Error saving integration:', error);
      setTestResult('Error: Failed to save integration.');
    } finally {
      setLocalLoading(false);
    }
  };

  const testConnection = async () => {
    if (!tenantId || !clientId || !appTenantId) {
      setTestResult('Error: Tenant ID and Client ID are required.');
      return;
    }

    // For new integrations, client secret is required
    if (!existingIntegration && !clientSecret) {
      setTestResult('Error: Client Secret is required for new integrations.');
      return;
    }

    setLocalLoading(true);
    try {
      // Use the Entra ID tenant ID (stored in tenantId state) for testing connection
      const isConnected = await testEntraIdConnection(tenantId, clientId, clientSecret);
      setTestResult(isConnected ? 'Connection successful!' : 'Connection failed. Please check your credentials.');
    } catch (error) {
      console.error('Error testing connection:', error);
      setTestResult('Error: Failed to test connection.');
    } finally {
      setLocalLoading(false);
    }
  };

  // Check if any required dependencies are missing
  if (!supabase) {
    console.error('Supabase client is not initialized');
    return (
      <div style={{ padding: '20px', margin: '20px', backgroundColor: '#ffebee', border: '1px solid #ffcdd2', borderRadius: '5px' }}>
        <h2>Error: Supabase Client Not Initialized</h2>
        <p>The Supabase client is not properly initialized. Please check your configuration.</p>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <h1 className={styles.title}>Integration Setup</h1>

      {contextError && !existingIntegration && (
        <div className={styles.error}>
          <strong>Error:</strong> {contextError}
        </div>
      )}

      <div className={styles.content}>
        {contextLoading && <p className={styles.loading}>Loading from context...</p>}
        {localLoading && <p className={styles.loading}>Loading locally...</p>}

        <div className={styles.formGroup}>
          <p>
            {existingIntegration
              ? 'Your Microsoft Entra ID integration is configured. You can update it below.'
              : 'Configure your Microsoft Entra ID integration below to connect your tenant with Rolewise.ai.'}
          </p>

          {existingIntegration && !isEditMode && (
            <button
              onClick={() => setIsEditMode(true)}
              className={styles.button}
            >
              Edit Configuration
            </button>
          )}
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>
            Entra ID Tenant ID:
            <input
              type="text"
              value={tenantId}
              onChange={(e) => setTenantId(e.target.value)}
              disabled={localLoading || contextLoading || (existingIntegration && !isEditMode)}
              className={styles.input}
              placeholder="Enter your Entra ID Tenant ID"
            />
          </label>
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>
            Client ID:
            <input
              type="text"
              value={clientId}
              onChange={(e) => setClientId(e.target.value)}
              disabled={localLoading || contextLoading || (existingIntegration && !isEditMode)}
              className={styles.input}
              placeholder="Enter your Client ID"
            />
          </label>
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>
            Client Secret:
            <input
              type="password"
              value={clientSecret}
              onChange={(e) => setClientSecret(e.target.value)}
              disabled={localLoading || contextLoading || (existingIntegration && !isEditMode)}
              className={styles.input}
              placeholder={existingIntegration ? "Enter new Client Secret (leave blank to keep current)" : "Enter your Client Secret"}
            />
          </label>
        </div>

        <div className={styles.buttonGroup}>
          {(!existingIntegration || isEditMode) && (
            <button
              onClick={saveIntegration}
              disabled={localLoading || contextLoading || !appTenantId}
              className={styles.button}
            >
              {localLoading ? 'Saving...' : existingIntegration ? 'Update Integration' : 'Save Integration'}
            </button>
          )}

          {isEditMode && (
            <button
              onClick={() => setIsEditMode(false)}
              disabled={localLoading || contextLoading}
              className={styles.button}
            >
              Cancel
            </button>
          )}

          <button
            onClick={testConnection}
            disabled={localLoading || contextLoading || !tenantId || !clientId || (!clientSecret && !existingIntegration) || !appTenantId}
            className={styles.button}
          >
            {localLoading ? 'Testing...' : 'Test Connection'}
          </button>
        </div>

        {testResult && (
          <div className={`${styles.result} ${testResult.includes('Error') ? styles.error : styles.success}`}>
            {testResult}
          </div>
        )}
      </div>
    </div>
  );
};

export default IntegrationSetup;