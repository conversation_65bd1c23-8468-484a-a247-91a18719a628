import { useState, useEffect } from 'react';

import { supabase } from '../services/supabaseClient';
import { UserProfile, TableRow } from '../types/supabase';
import { useAuthStore } from '../stores/authStore';
import * as styles from './usersStyles.css.ts';
import { AuthStatus } from '../types/auth';

interface UserWithRole extends UserProfile {
  role?: string;
}

const Users: React.FC = () => {
  const { user } = useAuthStore();
  const [users, setUsers] = useState<UserWithRole[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [roleFilter, setRoleFilter] = useState<string>('');

  useEffect(() => {
    const fetchUsers = async () => {
      if (!user?.user_metadata?.tenant_id) {
        setError('No tenant information available.');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        let query = supabase
          .from('profiles')
          .select(`
            id,
            name,
            email,
            role_id,
            tenant_id,
            created_at
          `)
          .eq('tenant_id', user?.user_metadata?.tenant_id);

        if (roleFilter) {
          query = query.eq('role_id', roleFilter);
        }

        const { data, error: fetchError } = await query;

        if (fetchError) {
          throw fetchError;
        }

        if (!data) {
          setUsers([]);
          return;
        }

        const filteredUsers = data.filter(user => 
          !searchTerm || 
          user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.email.toLowerCase().includes(searchTerm.toLowerCase())
        );

        setUsers(filteredUsers as UserWithRole[]);
      } catch (err) {
        console.error('Error fetching users:', err);
        setError('Failed to load users. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [user, roleFilter, searchTerm]);

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleRoleFilter = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setRoleFilter(event.target.value);
  };

  if (loading) {
    return <div className={styles.loading}>Loading users...</div>;
  }

  if (error) {
    return <div className={styles.error}>{error}</div>;
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1>Users</h1>
        <div className={styles.filters}>
          <input
            type="text"
            placeholder="Search users..."
            value={searchTerm}
            onChange={handleSearch}
            className={styles.searchInput}
          />
          <select
            value={roleFilter}
            onChange={handleRoleFilter}
            className={styles.roleFilter}
          >
            <option value="">All Roles</option>
            <option value="admin">Admin</option>
            <option value="user">User</option>
          </select>
        </div>
      </div>

      {users.length === 0 ? (
        <div className={styles.noResults}>
          {searchTerm || roleFilter ? 'No users match your search criteria.' : 'No users found.'}
        </div>
      ) : (
        <div className={styles.userGrid}>
          {users.map(user => (
            <div key={user.id} className={styles.userCard}>
              <div className={styles.userName}>{user.name || 'Unnamed User'}</div>
              <div className={styles.userEmail}>{user.email}</div>
              <div className={styles.userRole}>{user.role || 'No Role'}</div>
              <div className={styles.userCreated}>
                Joined: {new Date(user.created_at).toLocaleDateString()}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default Users;