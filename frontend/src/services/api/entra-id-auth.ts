import { PublicClientApplication, Configuration, InteractionRequiredAuthError, AccountInfo } from '@azure/msal-browser';

// Map to store MSAL instances per tenant/client combination
const msalInstances = new Map<string, PublicClientApplication>();

// Map to store authentication promises per tenant/client combination
const authPromises = new Map<string, Promise<string>>();

/**
 * Creates a unique key for tenant/client combination
 */
function createInstanceKey(tenantId: string, clientId: string): string {
  return `${tenantId}:${clientId}`;
}

/**
 * Gets or creates the MSAL instance for a specific tenant/client combination
 * This ensures complete isolation between different Entra ID integrations
 * @param tenantId The Entra ID tenant ID
 * @param clientId The Entra ID client ID
 * @returns The MSAL instance
 */
export async function getMsalInstance(tenantId: string, clientId: string): Promise<PublicClientApplication> {
  const instanceKey = createInstanceKey(tenantId, clientId);

  if (msalInstances.has(instanceKey)) {
    return msalInstances.get(instanceKey)!;
  }

  const msalConfig: Configuration = {
    auth: {
      clientId: clientId,
      authority: `https://login.microsoftonline.com/${tenantId}`,
      // Use a specific redirect URI for integrations to avoid conflicts with Auth0
      redirectUri: `${window.location.origin}/integration-callback`,
    },
    cache: {
      // Use a unique cache location per tenant to avoid conflicts
      cacheLocation: 'sessionStorage',
      storeAuthStateInCookie: false,
    },
    system: {
      // Add unique identifier to avoid conflicts
      loggerOptions: {
        loggerCallback: (level, message, containsPii) => {
          if (!containsPii) {
            console.log(`[MSAL-${instanceKey}] ${message}`);
          }
        }
      }
    }
  };

  const instance = new PublicClientApplication(msalConfig);
  await instance.initialize();

  msalInstances.set(instanceKey, instance);
  return instance;
}

/**
 * Internal function to perform the actual token acquisition
 * Isolated per tenant/client combination to avoid cross-contamination
 */
async function acquireTokenInternal(tenantId: string, clientId: string): Promise<string> {
  const instance = await getMsalInstance(tenantId, clientId);
  const instanceKey = createInstanceKey(tenantId, clientId);

  try {
    // Check if there's an account already signed in for this specific tenant/client
    const accounts = instance.getAllAccounts();
    let account: AccountInfo | null = null;

    // Filter accounts to only those belonging to this specific tenant
    const tenantAccounts = accounts.filter(acc =>
      acc.tenantId === tenantId || acc.homeAccountId?.includes(tenantId)
    );

    if (tenantAccounts.length === 0) {
      // No account for this tenant, initiate login with specific tenant context
      console.log(`[Integration Auth] No account found for tenant ${tenantId}, initiating login`);
      const loginResponse = await instance.loginPopup({
        scopes: ['https://graph.microsoft.com/.default'],
        prompt: 'select_account',
        // Ensure we're authenticating to the correct tenant
        extraQueryParameters: {
          tenant: tenantId
        }
      });
      account = loginResponse.account;
      instance.setActiveAccount(account);
    } else {
      // Use the first account for this tenant
      account = tenantAccounts[0];
      instance.setActiveAccount(account);
      console.log(`[Integration Auth] Using existing account for tenant ${tenantId}`);
    }

    // Acquire token silently for this specific account and tenant
    const tokenResponse = await instance.acquireTokenSilent({
      scopes: ['https://graph.microsoft.com/.default'],
      account,
      // Ensure token is for the correct tenant
      authority: `https://login.microsoftonline.com/${tenantId}`
    });

    console.log(`[Integration Auth] Successfully acquired token for tenant ${tenantId}`);
    return tokenResponse.accessToken;
  } catch (error) {
    console.log(`[Integration Auth] Silent token acquisition failed for tenant ${tenantId}:`, error);

    if (error instanceof InteractionRequiredAuthError) {
      // Fallback to popup if silent token acquisition fails
      console.log(`[Integration Auth] Attempting interactive token acquisition for tenant ${tenantId}`);
      const tokenResponse = await instance.acquireTokenPopup({
        scopes: ['https://graph.microsoft.com/.default'],
        prompt: 'select_account',
        authority: `https://login.microsoftonline.com/${tenantId}`,
        extraQueryParameters: {
          tenant: tenantId
        }
      });
      return tokenResponse.accessToken;
    }
    throw error;
  }
}

/**
 * Gets an access token for Microsoft Graph API for a specific tenant/client combination
 * This function ensures that authentication is isolated per integration
 * @param tenantId The Entra ID tenant ID
 * @param clientId The Entra ID client ID
 * @returns The access token
 */
export async function getAccessToken(tenantId: string, clientId: string): Promise<string> {
  const instanceKey = createInstanceKey(tenantId, clientId);

  // If there's already an authentication in progress for this tenant/client, wait for it
  if (authPromises.has(instanceKey)) {
    try {
      return await authPromises.get(instanceKey)!;
    } catch (error) {
      // If the previous attempt failed, clear it and try again
      authPromises.delete(instanceKey);
    }
  }

  // Start a new authentication attempt for this specific tenant/client
  const authPromise = acquireTokenInternal(tenantId, clientId);
  authPromises.set(instanceKey, authPromise);

  try {
    const token = await authPromise;
    return token;
  } finally {
    // Clear the promise when done (success or failure)
    authPromises.delete(instanceKey);
  }
}

/**
 * Tests the connection to Entra ID using the appropriate authentication method
 * @param tenantId The Entra ID tenant ID
 * @param clientId The Entra ID client ID
 * @param clientSecret The Entra ID client secret (optional)
 * @returns True if the connection is successful
 */
export async function testEntraIdConnection(tenantId: string, clientId: string, clientSecret?: string): Promise<boolean> {
  try {
    if (!clientSecret) {
      // If no client secret provided, try MSAL flow (user authentication)
      console.log(`[Integration Auth] Testing connection with user authentication for tenant ${tenantId}`);
      await getAccessToken(tenantId, clientId);
      return true;
    }

    // Test connection using Supabase edge function (app authentication)
    console.log(`[Integration Auth] Testing connection with app authentication for tenant ${tenantId}`);

    // Get the user's Auth0 token for authenticated API calls
    const { useAuthStore } = await import('../../stores/authStore');
    const { accessToken } = useAuthStore.getState();

    if (!accessToken) {
      console.error('[Integration Auth] No Auth0 token available for API call');
      return false;
    }

    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/get-entra-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`, // Use Auth0 token instead of anon key
      },
      body: JSON.stringify({
        tenant_id: tenantId,
        client_id: clientId,
        client_secret: clientSecret,
        test_only: true
      })
    });

    if (!response.ok) {
      console.error(`[Integration Auth] API call failed with status ${response.status}`);
      return false;
    }

    const data = await response.json();
    return data.success === true;
  } catch (error) {
    console.error('[Integration Auth] Connection test failed:', error);
    return false;
  }
}

/**
 * Clears authentication state for a specific tenant/client combination
 * Useful for logout or when switching between integrations
 */
export async function clearIntegrationAuth(tenantId: string, clientId: string): Promise<void> {
  const instanceKey = createInstanceKey(tenantId, clientId);

  try {
    // Clear any pending auth promises
    authPromises.delete(instanceKey);

    // Get the MSAL instance and clear its accounts
    if (msalInstances.has(instanceKey)) {
      const instance = msalInstances.get(instanceKey)!;
      const accounts = instance.getAllAccounts();

      // Clear accounts for this tenant
      for (const account of accounts) {
        if (account.tenantId === tenantId) {
          await instance.logoutPopup({
            account,
            postLogoutRedirectUri: window.location.origin
          });
        }
      }

      // Remove the instance
      msalInstances.delete(instanceKey);
    }

    console.log(`[Integration Auth] Cleared authentication state for tenant ${tenantId}`);
  } catch (error) {
    console.error(`[Integration Auth] Error clearing auth state for tenant ${tenantId}:`, error);
  }
}

/**
 * Clears all integration authentication state
 * Useful for complete cleanup
 */
export async function clearAllIntegrationAuth(): Promise<void> {
  console.log('[Integration Auth] Clearing all integration authentication state');

  // Clear all auth promises
  authPromises.clear();

  // Clear all MSAL instances
  for (const [key, instance] of msalInstances.entries()) {
    try {
      const accounts = instance.getAllAccounts();
      for (const account of accounts) {
        await instance.logoutPopup({
          account,
          postLogoutRedirectUri: window.location.origin
        });
      }
    } catch (error) {
      console.error(`[Integration Auth] Error clearing instance ${key}:`, error);
    }
  }

  msalInstances.clear();
}

/**
 * Gets the current authentication status for a specific integration
 */
export function getIntegrationAuthStatus(tenantId: string, clientId: string): {
  isAuthenticated: boolean;
  hasActiveAccount: boolean;
  accountInfo?: AccountInfo;
} {
  const instanceKey = createInstanceKey(tenantId, clientId);

  if (!msalInstances.has(instanceKey)) {
    return { isAuthenticated: false, hasActiveAccount: false };
  }

  const instance = msalInstances.get(instanceKey)!;
  const accounts = instance.getAllAccounts();
  const tenantAccounts = accounts.filter(acc =>
    acc.tenantId === tenantId || acc.homeAccountId?.includes(tenantId)
  );

  const hasActiveAccount = tenantAccounts.length > 0;
  const accountInfo = hasActiveAccount ? tenantAccounts[0] : undefined;

  return {
    isAuthenticated: hasActiveAccount,
    hasActiveAccount,
    accountInfo
  };
}

// Handle cleanup when the module is hot-reloaded
if (import.meta.hot) {
  import.meta.hot.dispose(() => {
    // Clear all instances and promises on hot reload
    msalInstances.clear();
    authPromises.clear();
  });
}