import { IntegrationConfig } from '../contexts/EntraIdIntegrationContext';

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ValidationWarning {
  field: string;
  message: string;
  code: string;
}

export interface PermissionCheckResult {
  hasRequiredPermissions: boolean;
  missingPermissions: string[];
  grantedPermissions: string[];
  needsAdminConsent: boolean;
}

// Required Microsoft Graph permissions
export const REQUIRED_PERMISSIONS = [
  'Application.Read.All',
  'Group.Read.All',
  'User.Read.All',
  'Directory.Read.All'
];

/**
 * Validates integration configuration
 */
export function validateIntegrationConfig(config: IntegrationConfig, authMode: 'app' | 'user', isEditMode: boolean = false): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];

  // Validate Tenant ID
  const tenantIdValidation = validateTenantId(config.tenantId);
  if (!tenantIdValidation.isValid) {
    errors.push({
      field: 'tenantId',
      message: tenantIdValidation.message,
      code: tenantIdValidation.code
    });
  }

  // Validate Client ID
  const clientIdValidation = validateClientId(config.clientId);
  if (!clientIdValidation.isValid) {
    errors.push({
      field: 'clientId',
      message: clientIdValidation.message,
      code: clientIdValidation.code
    });
  }

  // Validate Client Secret (for app authentication)
  if (authMode === 'app') {
    const clientSecretValidation = validateClientSecret(config.clientSecret || '', isEditMode);
    if (!clientSecretValidation.isValid) {
      errors.push({
        field: 'clientSecret',
        message: clientSecretValidation.message,
        code: clientSecretValidation.code
      });
    }

    // Warning for client secret expiration
    if (config.clientSecret && clientSecretValidation.warnings.length > 0) {
      warnings.push(...clientSecretValidation.warnings.map(w => ({
        field: 'clientSecret',
        message: w,
        code: 'CLIENT_SECRET_WARNING'
      })));
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Validates Tenant ID format and structure
 */
function validateTenantId(tenantId: string): { isValid: boolean; message: string; code: string } {
  if (!tenantId || !tenantId.trim()) {
    return {
      isValid: false,
      message: 'Tenant ID is required',
      code: 'TENANT_ID_REQUIRED'
    };
  }

  const trimmedId = tenantId.trim();

  // Check for valid GUID format
  const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!guidRegex.test(trimmedId)) {
    return {
      isValid: false,
      message: 'Tenant ID must be a valid GUID format (e.g., 12345678-1234-1234-1234-123456789abc)',
      code: 'TENANT_ID_INVALID_FORMAT'
    };
  }

  // Check for common test/placeholder values
  const testValues = [
    '00000000-0000-0000-0000-000000000000',
    '12345678-1234-1234-1234-123456789abc',
    'your-tenant-id-here'
  ];

  if (testValues.includes(trimmedId.toLowerCase())) {
    return {
      isValid: false,
      message: 'Please enter your actual Tenant ID, not a placeholder value',
      code: 'TENANT_ID_PLACEHOLDER'
    };
  }

  return { isValid: true, message: '', code: '' };
}

/**
 * Validates Client ID format and structure
 */
function validateClientId(clientId: string): { isValid: boolean; message: string; code: string } {
  if (!clientId || !clientId.trim()) {
    return {
      isValid: false,
      message: 'Client ID is required',
      code: 'CLIENT_ID_REQUIRED'
    };
  }

  const trimmedId = clientId.trim();

  // Check for valid GUID format
  const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!guidRegex.test(trimmedId)) {
    return {
      isValid: false,
      message: 'Client ID must be a valid GUID format (e.g., 12345678-1234-1234-1234-123456789abc)',
      code: 'CLIENT_ID_INVALID_FORMAT'
    };
  }

  // Check for common test/placeholder values
  const testValues = [
    '00000000-0000-0000-0000-000000000000',
    '12345678-1234-1234-1234-123456789abc',
    'your-client-id-here'
  ];

  if (testValues.includes(trimmedId.toLowerCase())) {
    return {
      isValid: false,
      message: 'Please enter your actual Client ID, not a placeholder value',
      code: 'CLIENT_ID_PLACEHOLDER'
    };
  }

  return { isValid: true, message: '', code: '' };
}

/**
 * Validates Client Secret format and provides warnings
 */
function validateClientSecret(clientSecret: string, isEditMode: boolean): { 
  isValid: boolean; 
  message: string; 
  code: string; 
  warnings: string[] 
} {
  const warnings: string[] = [];

  if (!isEditMode && (!clientSecret || !clientSecret.trim())) {
    return {
      isValid: false,
      message: 'Client Secret is required for app authentication',
      code: 'CLIENT_SECRET_REQUIRED',
      warnings
    };
  }

  if (clientSecret && clientSecret.trim()) {
    const trimmedSecret = clientSecret.trim();

    // Check minimum length
    if (trimmedSecret.length < 10) {
      return {
        isValid: false,
        message: 'Client Secret appears to be too short (minimum 10 characters)',
        code: 'CLIENT_SECRET_TOO_SHORT',
        warnings
      };
    }

    // Check for common placeholder values
    const placeholderValues = [
      'your-client-secret',
      'client-secret-here',
      'secret123',
      'password123'
    ];

    if (placeholderValues.some(placeholder => trimmedSecret.toLowerCase().includes(placeholder))) {
      return {
        isValid: false,
        message: 'Please enter your actual Client Secret, not a placeholder value',
        code: 'CLIENT_SECRET_PLACEHOLDER',
        warnings
      };
    }

    // Warnings for potentially weak secrets
    if (trimmedSecret.length < 20) {
      warnings.push('Client Secret is shorter than recommended (20+ characters)');
    }

    if (!/[A-Z]/.test(trimmedSecret) || !/[a-z]/.test(trimmedSecret) || !/[0-9]/.test(trimmedSecret)) {
      warnings.push('Client Secret should contain uppercase, lowercase, and numeric characters');
    }
  }

  return { isValid: true, message: '', code: '', warnings };
}

/**
 * Checks if the provided configuration has the required Microsoft Graph permissions
 */
export async function checkPermissions(config: IntegrationConfig, accessToken: string): Promise<PermissionCheckResult> {
  try {
    // Query Microsoft Graph to check app permissions
    const response = await fetch('https://graph.microsoft.com/v1.0/me/appRoleAssignments', {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Permission check failed: ${response.status}`);
    }

    const data = await response.json();
    const grantedPermissions = data.value?.map((assignment: any) => assignment.appRoleId) || [];

    // Map role IDs to permission names (this would need to be expanded with actual Microsoft Graph role IDs)
    const permissionMapping: Record<string, string> = {
      // These would be the actual Microsoft Graph application role IDs
      // This is a simplified example - in practice, you'd need the actual GUIDs
    };

    const grantedPermissionNames = grantedPermissions
      .map((roleId: string) => permissionMapping[roleId])
      .filter(Boolean);

    const missingPermissions = REQUIRED_PERMISSIONS.filter(
      permission => !grantedPermissionNames.includes(permission)
    );

    return {
      hasRequiredPermissions: missingPermissions.length === 0,
      missingPermissions,
      grantedPermissions: grantedPermissionNames,
      needsAdminConsent: missingPermissions.length > 0
    };
  } catch (error) {
    console.error('Permission check failed:', error);
    return {
      hasRequiredPermissions: false,
      missingPermissions: REQUIRED_PERMISSIONS,
      grantedPermissions: [],
      needsAdminConsent: true
    };
  }
}

/**
 * Validates that the tenant and client combination is accessible
 */
export async function validateTenantAccess(tenantId: string, clientId: string): Promise<{
  isAccessible: boolean;
  error?: string;
  tenantInfo?: {
    displayName: string;
    defaultDomain: string;
  };
}> {
  try {
    // This would make a request to the tenant's well-known configuration endpoint
    const wellKnownUrl = `https://login.microsoftonline.com/${tenantId}/v2.0/.well-known/openid_configuration`;
    
    const response = await fetch(wellKnownUrl);
    if (!response.ok) {
      return {
        isAccessible: false,
        error: 'Tenant not found or not accessible'
      };
    }

    const config = await response.json();
    
    // Extract tenant information from the issuer URL
    const issuer = config.issuer;
    const tenantMatch = issuer.match(/\/([0-9a-f-]+)\/v2\.0$/);
    
    if (!tenantMatch || tenantMatch[1] !== tenantId) {
      return {
        isAccessible: false,
        error: 'Tenant ID mismatch'
      };
    }

    return {
      isAccessible: true,
      tenantInfo: {
        displayName: 'Microsoft Entra ID Tenant', // Would need additional API call to get actual name
        defaultDomain: `${tenantId}.onmicrosoft.com`
      }
    };
  } catch (error) {
    return {
      isAccessible: false,
      error: `Tenant validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Provides user-friendly error messages for common integration issues
 */
export function getErrorGuidance(error: ValidationError): {
  title: string;
  description: string;
  actionItems: string[];
} {
  switch (error.code) {
    case 'TENANT_ID_REQUIRED':
      return {
        title: 'Tenant ID Missing',
        description: 'Your Microsoft Entra ID Tenant ID is required to establish the connection.',
        actionItems: [
          'Go to Azure Portal → Microsoft Entra ID → Overview',
          'Copy the "Tenant ID" value',
          'Paste it into the Tenant ID field'
        ]
      };

    case 'TENANT_ID_INVALID_FORMAT':
      return {
        title: 'Invalid Tenant ID Format',
        description: 'The Tenant ID must be in GUID format.',
        actionItems: [
          'Verify you copied the complete Tenant ID',
          'Ensure it follows the format: 12345678-1234-1234-1234-123456789abc',
          'Check for any extra spaces or characters'
        ]
      };

    case 'CLIENT_ID_REQUIRED':
      return {
        title: 'Client ID Missing',
        description: 'The Application (Client) ID from your app registration is required.',
        actionItems: [
          'Go to Azure Portal → Microsoft Entra ID → App registrations',
          'Select your app registration',
          'Copy the "Application (client) ID" from the Overview page'
        ]
      };

    case 'CLIENT_SECRET_REQUIRED':
      return {
        title: 'Client Secret Required',
        description: 'A client secret is required for app authentication.',
        actionItems: [
          'Go to your app registration → Certificates & secrets',
          'Create a new client secret',
          'Copy the secret value immediately (it won\'t be shown again)',
          'Paste it into the Client Secret field'
        ]
      };

    default:
      return {
        title: 'Configuration Error',
        description: error.message,
        actionItems: [
          'Review the error message above',
          'Check the Integration Setup Guide for detailed instructions',
          'Verify your app registration configuration in Azure Portal'
        ]
      };
  }
}
