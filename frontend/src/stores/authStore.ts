import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { useAuth0 } from '@auth0/auth0-react';
import { supabase } from '../services/supabaseClient';
import { UserProfile } from '../types/supabase';
import { AuthStatus } from '../types/auth';

// Auth0 User type (simplified)
interface Auth0User {
  sub: string;
  email?: string;
  name?: string;
  picture?: string;
  internalUserId?: string; // Internal UUID for database queries
  [key: string]: any;
}

// Define the state type for Auth0
interface AuthState {
  user: Auth0User | null;
  status: AuthStatus;
  userDetails: UserProfile | null;
  tenantId: string | null;
  permissions: string[];
  accessToken: string | null;
  setUser: (user: Auth0User | null) => void;
  setAccessToken: (token: string | null) => void;
  setUserDetails: (details: UserProfile | null) => void;
  setTenantId: (tenantId: string | null) => void;
  setPermissions: (permissions: string[]) => void;
  setStatus: (status: AuthStatus) => void;
  clearAuth: () => void;
}

type AuthStore = AuthState;

const fetchUserProfile = async (userId: string, userEmail: string, userName?: string): Promise<UserProfile | null> => {
  try {
    // First check if the profiles table exists
    const { error: tableCheckError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);

    if (tableCheckError) {
      if (tableCheckError.code === '42P01') {
        // Create a default profile in memory
        return {
          id: userId,
          name: userName || 'Default User',
          email: userEmail,
          tenant_id: 'default',
          role_id: 'default',
          created_at: new Date().toISOString()
        };
      }
      throw tableCheckError;
    }

    const { data, error } = await supabase
      .from('profiles')
      .select('id, name, email, tenant_id, role_id, created_at')
      .eq('id', userId)
      .maybeSingle();

    if (error) {
      throw error;
    }

    if (!data) {
      // Create a default profile if it doesn't exist
      const { error: createError } = await supabase
        .from('profiles')
        .insert([
          {
            id: userId,
            name: userName || 'Default User',
            email: userEmail,
            tenant_id: 'default',
            role_id: 'default',
            created_at: new Date().toISOString()
          }
        ]);

      if (createError) {
        throw new Error('Failed to create user profile. Please contact support.');
      }

      // Fetch the newly created profile
      const { data: newProfile, error: fetchError } = await supabase
        .from('profiles')
        .select('id, name, email, tenant_id, role_id, created_at')
        .eq('id', userId)
        .single();

      if (fetchError || !newProfile) {
        throw new Error('Failed to retrieve user profile. Please try again.');
      }

      return newProfile as UserProfile;
    }

    return data as UserProfile;
  } catch (error) {
    console.error('Failed to fetch user profile:', error);
    // Return a default profile on error
    return {
      id: userId,
      name: userName || 'Default User',
      email: userEmail,
      tenant_id: 'default',
      role_id: 'default',
      created_at: new Date().toISOString()
    };
  }
};

const fetchUserPermissions = async (userId: string, tenantId: string): Promise<string[]> => {
  try {
    const { data, error } = await supabase
      .from('user_roles')
      .select('role_id')
      .eq('user_id', userId)
      .eq('tenant_id', tenantId);

    if (error) {
      // If the table doesn't exist, return default permissions
      if (error.code === '42P01') {
        return ['user']; // Default permission for new users
      }
      throw error;
    }
    return data.map(item => item.role_id);
  } catch (error) {
    console.error('Failed to fetch user permissions:', error);
    return ['user']; // Default permission on error
  }
};

// Create the store for Auth0 integration
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      status: AuthStatus.LOADING,
      userDetails: null,
      tenantId: null,
      permissions: [],
      accessToken: null,

      setUser: (user: Auth0User | null) => {
        set({ user });
      },

      setAccessToken: (accessToken: string | null) => {
        set({ accessToken });
      },

      setUserDetails: (userDetails: UserProfile | null) => {
        set({ userDetails });
      },

      setTenantId: (tenantId: string | null) => {
        set({ tenantId });
      },

      setPermissions: (permissions: string[]) => {
        set({ permissions });
      },

      setStatus: (status: AuthStatus) => {
        set({ status });
      },

      clearAuth: () => {
        set({
          user: null,
          status: AuthStatus.UNAUTHENTICATED,
          userDetails: null,
          tenantId: null,
          permissions: [],
          accessToken: null,
        });
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);

// Helper function to get internal user ID from Auth0 user ID
const getInternalUserId = async (auth0UserId: string): Promise<string | null> => {
  try {
    const { data, error } = await supabase
      .from('auth0_user_mapping')
      .select('internal_user_id')
      .eq('auth0_user_id', auth0UserId)
      .single();

    if (error) {
      console.error('Failed to get internal user ID:', error);
      return null;
    }

    return data?.internal_user_id || null;
  } catch (error) {
    console.error('Error getting internal user ID:', error);
    return null;
  }
};

// Helper function to initialize user profile and permissions with Auth0 user data
export const initializeUserProfile = async (auth0User: Auth0User, accessToken: string) => {
  try {
    const auth0UserId = auth0User.sub;
    const userEmail = auth0User.email || '';
    const userName = auth0User.name;

    // Get internal user ID from mapping table
    const internalUserId = await getInternalUserId(auth0UserId);
    if (!internalUserId) {
      console.error('No internal user ID found for Auth0 user:', auth0UserId);
      useAuthStore.getState().setStatus(AuthStatus.UNAUTHENTICATED);
      return;
    }

    // Fetch user profile using internal user ID
    const profile = await fetchUserProfile(internalUserId, userEmail, userName);
    if (!profile) {
      console.error('Failed to fetch or create profile');
      return;
    }

    // Fetch user permissions using internal user ID
    const permissions = await fetchUserPermissions(internalUserId, profile.tenant_id);

    // Update the auth store with Auth0 user but internal user ID for database queries
    useAuthStore.getState().setUser({
      ...auth0User,
      internalUserId // Add internal user ID to the Auth0 user object
    });
    useAuthStore.getState().setAccessToken(accessToken);
    useAuthStore.getState().setUserDetails(profile);
    useAuthStore.getState().setTenantId(profile.tenant_id);
    useAuthStore.getState().setPermissions(permissions);
    useAuthStore.getState().setStatus(AuthStatus.AUTHENTICATED);

  } catch (error) {
    console.error('Error during profile initialization:', error);
    useAuthStore.getState().setStatus(AuthStatus.UNAUTHENTICATED);
  }
};

// Helper function to clear auth state
export const clearAuthState = () => {
  useAuthStore.getState().clearAuth();
  localStorage.removeItem('auth-storage');
};

// Helper function to get the user ID for database queries
export const getDatabaseUserId = (): string | null => {
  const { user } = useAuthStore.getState();
  // Use internal user ID for database queries, fallback to Auth0 user ID
  return user?.internalUserId || user?.sub || null;
};